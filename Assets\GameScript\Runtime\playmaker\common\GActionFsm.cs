using System;
using HutongGames.PlayMaker;
using UnityEngine;

namespace Fish.PlayMaker
{
	[ActionCategory("Fish/Common")]
	[HutongGames.PlayMaker.Tooltip("GAction实现")]
	public class GActionFsm : FsmStateAction
	{
		[RequiredField]
		[HutongGames.PlayMaker.Tooltip("目标对象")]
		public FsmOwnerDefault targetObject;
		public PMGAction Action;
		[HutongGames.PlayMaker.Tooltip("动作完成时触发的事件")]
		public FsmEvent finishedEvent;

		private GAction _currentAction;
		private GameObject _targetGameObject;

		public override void Reset()
		{
			targetObject = null;
			_currentAction = null;
			Action = new PMGAction();
		}

		public override void OnEnter()
		{
			_targetGameObject = Fsm.GetOwnerDefaultTarget(targetObject);

			if (_targetGameObject == null)
			{
				GameLogger.LogWarning("Target object is null!");
				Finish();
				return;
			}

			_currentAction = CreateAction(Action);

			if (_currentAction != null)
			{
				_currentAction.StartWithTarget(_targetGameObject);
				// 先更新一次，触发一下instant类型的action逻辑
				_currentAction.Step(0);

				// 这里需要再次检查是否为空，因为_currentAction.Step以后，如果Fsm的Owner被隐藏了，
				// 会走OnExit，在那里会清空_currentAction，所以这里需要再次判断
				if (_currentAction != null)
				{
					if (_currentAction.IsDone())
					{
						ActionFinished();
					}
				}
			}
			else
			{
				GameLogger.LogWarning("Failed to create GAction for type: " + Action.ActionType);
				Finish();
			}
		}

		public override void OnUpdate()
		{
			if (_currentAction != null)
			{
				_currentAction.Step(Time.deltaTime);

				if (_currentAction.IsDone())
				{
					ActionFinished();
				}
			}
		}

		private void ActionFinished()
		{
			if (finishedEvent != null)
			{
				Fsm.Event(finishedEvent);
			}
			if (_currentAction != null)
			{
				_currentAction.Stop();
			}
			_currentAction = null;

			Finish();
		}

		public override void OnExit()
		{
			if (_currentAction != null)
			{
				_currentAction.Stop();
				_currentAction = null;
			}
		}

		private GAction CreateAction(PMGAction action)
		{
			return null;
		}

		public enum ActionType
		{
			Show,
			Hide,
			RemoveSelf,
			FlipX,
			FlipY,
			CallFunc,// todo:
			DelayTime,
			MoveBy,
			MoveTo,
			RotateBy,
			RotateTo,
			RotateBy3D,
			ScaleTo,
			ScaleBy,
			FadeTo,
			TintTo,
			BlendTo,
			Flash,
			BezierTo,
			CanvasGroupAlphaFadeTo,

			//高级节点，由于序列化问题，不支持嵌套在基础节点中
			Sequence,// todo:
			Spawn,// todo:
			Repeat,// todo:
			RepeatForever,// todo:
		}
	}

	[Serializable]
	public class PMGAction : ISerializationCallbackReceiver
	{
		public GActionFsm.ActionType ActionType;

		// 运行时使用的参数对象
		[System.NonSerialized]
		public PMGActionBase Param;

		// 用于序列化的字段
		[SerializeField]
		private string _paramDataJson;

		[NonSerialized]
		public bool foldout = true;

		public void OnActionTypeChanged(GActionFsm.ActionType newActionType)
		{
			ActionType = newActionType;

			if (newActionType == GActionFsm.ActionType.DelayTime)
			{
				Param = new PMGActionDelayTime().Init();
			}
			else if (newActionType == GActionFsm.ActionType.MoveTo)
			{
				Param = new PMGActionMoveTo().Init();
			}
			else if (newActionType == GActionFsm.ActionType.MoveBy)
			{
				Param = new PMGActionMoveBy().Init();
			}
			else if (newActionType == GActionFsm.ActionType.RotateBy)
			{
				Param = new PMGActionRotateBy().Init();
			}
			else if (newActionType == GActionFsm.ActionType.RotateTo)
			{
				Param = new PMGActionRotateTo().Init();
			}
			else if (newActionType == GActionFsm.ActionType.RotateBy3D)
			{
				Param = new PMGActionRotateBy3D().Init();
			}
			else if (newActionType == GActionFsm.ActionType.ScaleTo)
			{
				Param = new PMGActionScaleTo().Init();
			}
			else if (newActionType == GActionFsm.ActionType.ScaleBy)
			{
				Param = new PMGActionScaleBy().Init();
			}
			else if (newActionType == GActionFsm.ActionType.FadeTo)
			{
				Param = new PMGActionFadeTo().Init();
			}
			else if (newActionType == GActionFsm.ActionType.TintTo)
			{
				Param = new PMGActionTintTo().Init();
			}
			else if (newActionType == GActionFsm.ActionType.BlendTo)
			{
				Param = new PMGActionBlendTo().Init();
			}
			else if (newActionType == GActionFsm.ActionType.BezierTo)
			{
				Param = new PMGActionBezierTo().Init();
			}
			else if (newActionType == GActionFsm.ActionType.CanvasGroupAlphaFadeTo)
			{
				Param = new PMGActionCanvasGroupAlphaFadeTo().Init();
			}
			else if (newActionType == GActionFsm.ActionType.Sequence)
			{
				Param = new PMGActionSequence().Init();
			}
			else
			{
				Param = new PMGActionBase();
			}
		}

		// 序列化前调用
		public void OnBeforeSerialize()
		{
			if (Param != null)
			{
				_paramDataJson = UnityEngine.JsonUtility.ToJson(Param);
			}
		}

		// 反序列化后调用
		public void OnAfterDeserialize()
		{
			if (!string.IsNullOrEmpty(_paramDataJson))
			{
				// 根据ActionType创建对应的参数对象
				CreateParamByActionType();

				if (Param != null)
				{
					UnityEngine.JsonUtility.FromJsonOverwrite(_paramDataJson, Param);
				}
			}
			else
			{
				CreateParamByActionType();
			}
		}

		private void CreateParamByActionType()
		{
			switch (ActionType)
			{
				case GActionFsm.ActionType.DelayTime:
					Param = new PMGActionDelayTime().Init();
					break;
				case GActionFsm.ActionType.MoveTo:
					Param = new PMGActionMoveTo().Init();
					break;
				case GActionFsm.ActionType.MoveBy:
					Param = new PMGActionMoveBy().Init();
					break;
				case GActionFsm.ActionType.RotateBy:
					Param = new PMGActionRotateBy().Init();
					break;
				case GActionFsm.ActionType.RotateTo:
					Param = new PMGActionRotateTo().Init();
					break;
				case GActionFsm.ActionType.RotateBy3D:
					Param = new PMGActionRotateBy3D().Init();
					break;
				case GActionFsm.ActionType.ScaleTo:
					Param = new PMGActionScaleTo().Init();
					break;
				case GActionFsm.ActionType.ScaleBy:
					Param = new PMGActionScaleBy().Init();
					break;
				case GActionFsm.ActionType.FadeTo:
					Param = new PMGActionFadeTo().Init();
					break;
				case GActionFsm.ActionType.TintTo:
					Param = new PMGActionTintTo().Init();
					break;
				case GActionFsm.ActionType.BlendTo:
					break;
				case GActionFsm.ActionType.BezierTo:
					break;
				case GActionFsm.ActionType.CanvasGroupAlphaFadeTo:
					break;
				case GActionFsm.ActionType.Sequence:
					break;
				default:
					Param = new PMGActionBase();
					break;
			}
		}

		private void Reset()
		{
		}
	}

	[Serializable]
	public class PMGActionBase
	{

	}

	[Serializable]
	public class PMGActionDelayTime : PMGActionBase
	{
		public PMParam<float> duration;

		public PMGActionDelayTime Init()
		{
			duration = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionMoveBy : PMGActionBase
	{
		public PMParam<float> duration;
		public PMParam<Vector3> deltaPosition;
		public PMParam<bool> worldPosition;

		public PMGActionMoveBy Init()
		{
			duration = new PMParam<float>();
			deltaPosition = new PMParam<Vector3>();
			worldPosition = new PMParam<bool>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionMoveTo : PMGActionBase
	{
		public PMParam<float> duration;
		public PMParam<Vector3> target;
		public PMParam<bool> worldPosition;

		public PMGActionMoveTo Init()
		{
			duration = new PMParam<float>();
			target = new PMParam<Vector3>();
			worldPosition = new PMParam<bool>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionRotateBy : PMGActionBase
	{
		public PMParam<float> duration;
		public PMParam<float> deltaAngle;

		public PMGActionRotateBy Init()
		{
			duration = new PMParam<float>();
			deltaAngle = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionRotateTo : PMGActionBase
	{
		public PMParam<float> duration;
		public PMParam<float> destAngle;

		public PMGActionRotateTo Init()
		{
			duration = new PMParam<float>();
			destAngle = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionRotateBy3D : PMGActionBase
	{
		public PMParam<float> duration;
		public PMParam<float> deltaAngleX;
		public PMParam<float> deltaAngleY;
		public PMParam<float> deltaAngleZ;

		public PMGActionRotateBy3D Init()
		{
			duration = new PMParam<float>();
			deltaAngleX = new PMParam<float>();
			deltaAngleY = new PMParam<float>();
			deltaAngleZ = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionScaleTo : PMGActionBase
	{
		public PMParam<float> duration;
		public PMParam<float> sx;
		public PMParam<float> sy;
		public PMParam<float> sz;

		public PMGActionScaleTo Init()
		{
			duration = new PMParam<float>();
			sx = new PMParam<float>();
			sy = new PMParam<float>();
			sz = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionScaleBy : PMGActionBase
	{
		public PMParam<float> duration;
		public PMParam<float> sx;
		public PMParam<float> sy;
		public PMParam<float> sz;

		public PMGActionScaleBy Init()
		{
			duration = new PMParam<float>();
			sx = new PMParam<float>();
			sy = new PMParam<float>();
			sz = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionFadeTo : PMGActionBase
	{
		public PMParam<float> duration;
		public PMParam<float> alpha;

		public PMGActionFadeTo Init()
		{
			duration = new PMParam<float>();
			alpha = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionTintTo : PMGActionBase
	{
		public PMParam<float> duration;
		public PMParam<float> r;
		public PMParam<float> g;
		public PMParam<float> b;

		public PMGActionTintTo Init()
		{
			duration = new PMParam<float>();
			r = new PMParam<float>();
			g = new PMParam<float>();
			b = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionBlendTo
	{
		public PMParam<float> duration;
		public PMParam<float> a;
		public PMParam<float> r;
		public PMParam<float> g;
		public PMParam<float> b;

		public void Init()
		{
			duration = new PMParam<float>();
			a = new PMParam<float>();
			r = new PMParam<float>();
			g = new PMParam<float>();
			b = new PMParam<float>();
		}
	}

	[Serializable]
	public class PMGActionBezierTo
	{
		public PMParam<float> duration;
		public PMParam<Vector3> p0;
		public PMParam<Vector3> p1;
		public PMParam<Vector3> p2;
		public PMParam<Vector3> p3;

		public void Init()
		{
			duration = new PMParam<float>();
			p0 = new PMParam<Vector3>();
			p1 = new PMParam<Vector3>();
			p2 = new PMParam<Vector3>();
			p3 = new PMParam<Vector3>();
		}
	}

	[Serializable]
	public class PMGActionCanvasGroupAlphaFadeTo
	{
		public PMParam<float> duration;
		public PMParam<float> alpha;

		public void Init()
		{
			duration = new PMParam<float>();
			alpha = new PMParam<float>();
		}
	}

	[Serializable]
	public class PMGActionSequence
	{
		public PMGAction[] actions;

		// 编辑器折叠状态（不序列化）
		[NonSerialized]
		public bool foldout = true;

		public void Init()
		{
			actions = new PMGAction[0];
		}
	}


	[Serializable]
	public class PMParam<T>
	{
		public bool UseStoreData;
		public string StoreKey;
		public T RawValue;


		public T GetValue(GameObject gameObject)
		{
			if (UseStoreData)
			{
				var value = gameObject.GetDataObject(StoreKey);
				return (T)value;
			}
			else
			{
				return RawValue;
			}
		}
	}

}