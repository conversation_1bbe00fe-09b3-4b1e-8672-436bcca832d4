/target:library
/out:Temp/Assembly-CSharp-Editor.dll
/nowarn:0169
/nowarn:0649
/refout:Temp/Assembly-CSharp-Editor.dll.ref
/unsafe
/deterministic
/debug:portable
/optimize-
/nostdlib+
/preferreduilang:en-US
/langversion:8.0
/reference:Library/ScriptAssemblies/Assembly-CSharp.dll
/reference:Assets/PlayMaker/ConditionalExpression.dll
/reference:Assets/PlayMaker/Editor/ConditionalExpressionEditor.dll
/reference:Assets/PlayMaker/Editor/PlayMakerEditor.dll
/reference:Assets/PlayMaker/Editor/PlayMakerEditorResources.dll
/reference:Assets/Plugins/ConsoleE/Editor/ConsoleE.dll
/reference:Assets/Plugins/DOTween/DOTween.dll
/reference:Assets/Plugins/ICSharpCode.SharpZipLib/netstandard2.0/ICSharpCode.SharpZipLib.dll
/reference:Assets/Plugins/ILRuntime/Plugins/ILRuntime.Mono.Cecil.Mdb.dll
/reference:Assets/Plugins/ILRuntime/Plugins/ILRuntime.Mono.Cecil.Pdb.dll
/reference:Assets/Plugins/ILRuntime/Plugins/ILRuntime.Mono.Cecil.dll
/reference:Assets/Plugins/Ionic.Zlib.dll
/reference:Assets/Plugins/JsonDotNet/Assemblies/Standalone/Newtonsoft.Json.dll
/reference:Assets/Plugins/PlayMaker/PlayMaker.dll
/reference:Assets/Plugins/TexturePacker/Editor/TexturePackerImporter.dll
/reference:Assets/Plugins/zxing.unity.dll
/reference:Assets/ThirdPart/Proxima/WebSocketSharp/proxima-websocket-sharp.dll
/reference:Assets/UWA/UWA_GOT/Editor/UWALib.dll
/reference:Assets/UWA/UWA_SDK/Editor/UWAEditor.dll
/reference:Assets/UWA/UWA_SDK/Runtime/ManagedLibs/UWAShared.dll
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll"
/reference:C:/Users/<USER>/Documents/game/client/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll
/reference:C:/Users/<USER>/Documents/game/client/Packages/com.code-philosophy.hybridclr/Plugins/LZ4.dll
/reference:C:/Users/<USER>/Documents/game/client/Packages/com.code-philosophy.hybridclr/Plugins/dnlib.dll
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll"
/reference:"C:/Program Files/Unity/Hub/Editor/2020.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll"
/reference:Library/ScriptAssemblies/Assembly-CSharp-Editor-firstpass.dll
/reference:Library/ScriptAssemblies/Assembly-CSharp-firstpass.dll
/reference:Library/ScriptAssemblies/Coffee.CFX_Demo_With_UIParticle.dll
/reference:Library/ScriptAssemblies/Coffee.UIParticle.dll
/reference:Library/ScriptAssemblies/FMODUnity.dll
/reference:Library/ScriptAssemblies/FMODUnityEditor.dll
/reference:Library/ScriptAssemblies/FMODUnityResonance.dll
/reference:Library/ScriptAssemblies/FMODUnityResonanceEditor.dll
/reference:Library/ScriptAssemblies/GameRuntime.dll
/reference:Library/ScriptAssemblies/HybridCLR.Editor.dll
/reference:Library/ScriptAssemblies/HybridCLR.Runtime.dll
/reference:Library/ScriptAssemblies/MeshEditor.Effects.RunTime.dll
/reference:Library/ScriptAssemblies/NativeGallery.Editor.dll
/reference:Library/ScriptAssemblies/NativeGallery.Runtime.dll
/reference:Library/ScriptAssemblies/Proxima.Editor.dll
/reference:Library/ScriptAssemblies/Proxima.dll
/reference:Library/ScriptAssemblies/RBG.Mulligan.dll
/reference:Library/ScriptAssemblies/UniFramework.Animation.dll
/reference:Library/ScriptAssemblies/UniFramework.Event.dll
/reference:Library/ScriptAssemblies/UniFramework.Machine.dll
/reference:Library/ScriptAssemblies/UniFramework.Network.dll
/reference:Library/ScriptAssemblies/UniFramework.Pooling.dll
/reference:Library/ScriptAssemblies/UniFramework.Singleton.dll
/reference:Library/ScriptAssemblies/UniFramework.Tween.dll
/reference:Library/ScriptAssemblies/UniFramework.Utility.dll
/reference:Library/ScriptAssemblies/UniFramework.Window.dll
/reference:Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll
/reference:Library/ScriptAssemblies/Unity.Cursor.Editor.dll
/reference:Library/ScriptAssemblies/Unity.DeviceSimulator.Editor.dll
/reference:Library/ScriptAssemblies/Unity.Postprocessing.Editor.dll
/reference:Library/ScriptAssemblies/Unity.Postprocessing.Runtime.dll
/reference:Library/ScriptAssemblies/Unity.Rider.Editor.dll
/reference:Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.Editor.dll
/reference:Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.dll
/reference:Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll
/reference:Library/ScriptAssemblies/Unity.TextMeshPro.dll
/reference:Library/ScriptAssemblies/Unity.Timeline.Editor.dll
/reference:Library/ScriptAssemblies/Unity.Timeline.dll
/reference:Library/ScriptAssemblies/Unity.VSCode.Editor.dll
/reference:Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll
/reference:Library/ScriptAssemblies/UnityEditor.CacheServer.dll
/reference:Library/ScriptAssemblies/UnityEditor.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/reference:Library/ScriptAssemblies/YooAsset.Editor.dll
/reference:Library/ScriptAssemblies/YooAsset.dll
/reference:Library/ScriptAssemblies/spine-unity-editor.dll
/reference:Library/ScriptAssemblies/spine-unity.dll
/reference:Library/ScriptAssemblies/youhu.unity_uwa_sdk.Editor.dll
/reference:Library/ScriptAssemblies/youhu.unity_uwa_sdk.dll
/define:AMPLIFY_SHADER_EDITOR
/define:CSHARP_7_3_OR_NEWER
/define:CSHARP_7_OR_LATER
/define:DEBUG
/define:DISABLE_AIHELP
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_BURST_AOT
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:ENABLE_LOCALIZATION
/define:ENABLE_LZMA
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_UNITYTLS
/define:ENABLE_MICROPHONE
/define:ENABLE_MONO
/define:ENABLE_MONO_BDWGC
/define:ENABLE_MOVIES
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_NETWORK
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_PHYSICS
/define:ENABLE_PROFILER
/define:ENABLE_RUNTIME_GI
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_UNET
/define:ENABLE_UNITYEVENTS
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
/define:ENABLE_VIDEO
/define:ENABLE_VIRTUALTEXTURING
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_WWW
/define:FISH_DEBUG
/define:FISH_PAY
/define:FISH_PORTRAIT
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:INCLUDE_DYNAMIC_GI
/define:NET_4_6
/define:ODIN_INSPECTOR
/define:ODIN_INSPECTOR_3
/define:PLATFORM_ARCH_64
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_SUPPORTS_MONO
/define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
/define:PLAYMAKER
/define:PLAYMAKER_1_8_5_OR_NEWER
/define:PLAYMAKER_1_8_OR_NEWER
/define:PLAYMAKER_1_9
/define:PLAYMAKER_1_9_1
/define:PLAYMAKER_1_9_OR_NEWER
/define:PLAYMAKER_TMPRO
/define:PLAYMAKER_UTILS
/define:RENDER_SOFTWARE_CURSOR
/define:TRACE
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:UNITY_2020
/define:UNITY_2020_1_OR_NEWER
/define:UNITY_2020_2_OR_NEWER
/define:UNITY_2020_3
/define:UNITY_2020_3_47
/define:UNITY_2020_3_OR_NEWER
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_64
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_WIN
/define:UNITY_INCLUDE_TESTS
/define:UNITY_POST_PROCESSING_STACK_V2
/define:UNITY_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_TEAM_LICENSE
/define:USE_HUATUO
Assets\GameScript\Editor\ABBuilder\ABBuilder.cs
Assets\GameScript\Editor\ABBuilder\ABBuilderPackage.cs
Assets\GameScript\Editor\ABBuilder\ABBuilderServer.cs
Assets\GameScript\Editor\Analyze\Analyse.cs
Assets\GameScript\Editor\Analyze\AnalyseAllInuseAssets.cs
Assets\GameScript\Editor\Analyze\AnalyseAllUnuseAssets.cs
Assets\GameScript\Editor\Analyze\AnalyseBundle.cs
Assets\GameScript\Editor\Analyze\AnalyseFinder.cs
Assets\GameScript\Editor\Analyze\AnalyseILRBehaviour.cs
Assets\GameScript\Editor\Analyze\AnalyseMoveUtils.cs
Assets\GameScript\Editor\Analyze\AnalyseReferences.cs
Assets\GameScript\Editor\Analyze\AnalyseSprite.cs
Assets\GameScript\Editor\Analyze\AnalyseUtils.cs
Assets\GameScript\Editor\Analyze\AssetDisplay.cs
Assets\GameScript\Editor\Artist\ArtistTools.cs
Assets\GameScript\Editor\Artist\ParticleSystemScan.cs
Assets\GameScript\Editor\Artist\RemoveMissingScripts.cs
Assets\GameScript\Editor\Artist\ReplayJason.cs
Assets\GameScript\Editor\Artist\SVNTools.cs
Assets\GameScript\Editor\Artist\TexturePathComparer.cs
Assets\GameScript\Editor\AssetArtScanner\Profiler\AnalyzeParticleProfiler.cs
Assets\GameScript\Editor\AssetArtScanner\Profiler\AnalyzeTextureProfiler.cs
Assets\GameScript\Editor\AssetArtScanner\Schema\AssetBitmapSchema.cs
Assets\GameScript\Editor\AssetArtScanner\Schema\AssetFBXSchema.cs
Assets\GameScript\Editor\AssetArtScanner\Schema\AssetMaterialSchema.cs
Assets\GameScript\Editor\AssetArtScanner\Schema\AssetPrefabSchema.cs
Assets\GameScript\Editor\AssetArtScanner\Schema\AssetTextureSchema.cs
Assets\GameScript\Editor\AssetArtScanner\Schema\BasePackageSchema.cs
Assets\GameScript\Editor\AssetArtScanner\Schema\DependencySchema.cs
Assets\GameScript\Editor\AssetArtScanner\Schema\ReferenceSchema.cs
Assets\GameScript\Editor\AssetArtScanner\Tools\AnimationTools.cs
Assets\GameScript\Editor\AssetArtScanner\Tools\MaterialTools.cs
Assets\GameScript\Editor\AssetArtScanner\Tools\ModelTools.cs
Assets\GameScript\Editor\AssetArtScanner\Tools\PrefabTools.cs
Assets\GameScript\Editor\AssetArtScanner\Tools\RegularTools.cs
Assets\GameScript\Editor\AssetArtScanner\Tools\SchemaTools.cs
Assets\GameScript\Editor\AssetArtScanner\Tools\ShaderTools.cs
Assets\GameScript\Editor\AssetArtScanner\Tools\TextureTools.cs
Assets\GameScript\Editor\AssetBuildPrepareTools\AssetBuildPrepareTools.cs
Assets\GameScript\Editor\AssetBuildPrepareTools\PrefabManifestPrepareTools.cs
Assets\GameScript\Editor\AssetBuildPrepareTools\TaskBindingILRuntime.cs
Assets\GameScript\Editor\AssetBuildPrepareTools\TaskCheckBuildTxt.cs
Assets\GameScript\Editor\AssetBuildPrepareTools\TaskCheckPanelFolderName.cs
Assets\GameScript\Editor\AssetBuildPrepareTools\TaskCopyBuildFile.cs
Assets\GameScript\Editor\AssetBuildPrepareTools\TaskCreatePrefabManifest.cs
Assets\GameScript\Editor\AssetBuildPrepareTools\TaskHuatuoDisable.cs
Assets\GameScript\Editor\AssetBuildPrepareTools\TaskHuatuoEnable.cs
Assets\GameScript\Editor\AssetBuildPrepareTools\TaskMappingMajiaAssets.cs
Assets\GameScript\Editor\AssetBuildPrepareTools\TaskReimportCollectXML.cs
Assets\GameScript\Editor\AssetBundle\AIHelpBuildPostProcessor.cs
Assets\GameScript\Editor\AssetBundle\AssetDependencyCache.cs
Assets\GameScript\Editor\AssetBundle\BuildPostProcessor.cs
Assets\GameScript\Editor\AssetBundle\BuildPostProcessorHW.cs
Assets\GameScript\Editor\AssetBundle\BuildTools.cs
Assets\GameScript\Editor\AssetBundle\BuildToolsHW.cs
Assets\GameScript\Editor\AssetBundle\BundleBuild.cs
Assets\GameScript\Editor\AssetBundle\BundleBuildNew.cs
Assets\GameScript\Editor\AssetBundle\BundleBuildNewHW.cs
Assets\GameScript\Editor\AssetBundle\BundleEncrypter.cs
Assets\GameScript\Editor\AssetBundle\BundleReport.cs
Assets\GameScript\Editor\AssetBundle\RuleExtension\ActiveRuleExtension.cs
Assets\GameScript\Editor\AssetBundle\RuleExtension\FilterRuleExtension.cs
Assets\GameScript\Editor\AssetBundle\RuleExtension\GroupRuleExtension.cs
Assets\GameScript\Editor\AssetBundle\RuleExtension\PackRuleExtension.cs
Assets\GameScript\Editor\AssetBundle\ShaderPreprocessor.cs
Assets\GameScript\Editor\ExportDependencies.cs
Assets\GameScript\Editor\FindHotScript\FindHotScriptWindow.cs
Assets\GameScript\Editor\FindMissing\FindMissingWindow.cs
Assets\GameScript\Editor\FindSkinAsset.cs
Assets\GameScript\Editor\FinderTools\FindMaterialsWithShader.cs
Assets\GameScript\Editor\FishExporter\FishExporter.cs
Assets\GameScript\Editor\GM\GMTools.cs
Assets\GameScript\Editor\HybridCLR\CLRTools.cs
Assets\GameScript\Editor\ILRuntime\ILRuntimeAssemblyBuilder.cs
Assets\GameScript\Editor\ILRuntime\ILRuntimeCLRBinding.cs
Assets\GameScript\Editor\ILRuntime\ILRuntimeInitialize.cs
Assets\GameScript\Editor\ILRuntimeBuildManager.cs
Assets\GameScript\Editor\International\ImportFntTool.cs
Assets\GameScript\Editor\International\ImportTool.cs
Assets\GameScript\Editor\International\MultiLanguage.cs
Assets\GameScript\Editor\International\TranslationTableExpand.cs
Assets\GameScript\Editor\International\TransplantWindow.cs
Assets\GameScript\Editor\International\Transplantor.cs
Assets\GameScript\Editor\ParticleFixer\ParticleFixer.cs
Assets\GameScript\Editor\ParticleProfiler\ParticleProfiler.cs
Assets\GameScript\Editor\ParticleProfiler\ParticleProfilerWindow.cs
Assets\GameScript\Editor\PatchAnalyzer\PatchAnalyzerWindow.cs
Assets\GameScript\Editor\PatchDecrypter\PatchDecrypterWindow.cs
Assets\GameScript\Editor\PlayMaker\GActionFsmEditor.cs
Assets\GameScript\Editor\PlayMaker\GActionRepeatFsmActionEditor.cs
Assets\GameScript\Editor\PrefabTextBrowser.cs
Assets\GameScript\Editor\ShaderGUI\CustomShaderGUI.cs
Assets\GameScript\Editor\ShaderGUI\CustomShaderGUI_Base.cs
Assets\GameScript\Editor\ShaderGUI\CustomShaderGUI_Test.cs
Assets\GameScript\Editor\ShaderGUI\CustomSpriteGUI.cs
Assets\GameScript\Editor\ShaderGUI\CustomStandardGUI.cs
Assets\GameScript\Editor\ShaderGUI\CustomStandardGUIV2.cs
Assets\GameScript\Editor\ShaderGUI\CustomStandardGUIV3.cs
Assets\GameScript\Editor\ShaderGUI\FishShaderGUI.cs
Assets\GameScript\Editor\ShaderGUI\PropertyDrawer.cs
Assets\GameScript\Editor\ShaderGUI\SEFullEffectInShowShaderGUI.cs
Assets\GameScript\Editor\ShaderGUI\TY_Effect_ParticleShaderGUI.cs
Assets\GameScript\Editor\TextureAssetsManager\TextureNameChecker.cs
Assets\GameScript\Editor\Tools\AssetsNameStandardization.cs
Assets\GameScript\Editor\Tools\AutoSetTrackFile.cs
Assets\GameScript\Editor\Tools\BossCoinExplodeNodeSetTool.cs
Assets\GameScript\Editor\Tools\ClearPlayerSetting.cs
Assets\GameScript\Editor\Tools\ClientConfigExpand.cs
Assets\GameScript\Editor\Tools\ClientLanguageExpand.cs
Assets\GameScript\Editor\Tools\CreateFnt.cs
Assets\GameScript\Editor\Tools\EditorPlayPrepare.cs
Assets\GameScript\Editor\Tools\EntitlementsPostProcess.cs
Assets\GameScript\Editor\Tools\HdAssetsMappingTools.cs
Assets\GameScript\Editor\Tools\HierarchyPathContextMenu.cs
Assets\GameScript\Editor\Tools\MatRefrenceChecker.cs
Assets\GameScript\Editor\Tools\MaterialSearchWindow.cs
Assets\GameScript\Editor\Tools\MergePrefab.cs
Assets\GameScript\Editor\Tools\PrefabEffectReplaceTool.cs
Assets\GameScript\Editor\Tools\PrefabTool2D.cs
Assets\GameScript\Editor\Tools\SpriteReferenceChecker.cs
Assets\GameScript\Editor\Tools\Sprites2Animation.cs
Assets\GameScript\Editor\Tools\TextureSetting.cs
Assets\GameScript\Editor\Tools\UICustomCreator.cs
Assets\GameScript\Editor\Tools\WingAndCannonShowTool.cs
Assets\GameScript\Editor\Trails\Editor\TrailEditor.cs
Assets\GameScript\Editor\Trails\Editor\TrailEditor_Base.cs
Assets\GameScript\Editor\Trails\Editor\TrailPreviewUtillity.cs
Assets\GameScript\Editor\UIAtlasProfiler\UIAtlasProfilerWindow.cs
Assets\GameScript\Editor\Utility\ApplicationMonitor.cs
Assets\GameScript\Editor\Utility\AssemblyUtility.cs
Assets\GameScript\Editor\Utility\FileUtility.cs
Assets\GameScript\Editor\YooScanner\Schema\AssetFBXSchema.cs
Assets\GameScript\Editor\YooScanner\Schema\AssetMaterialSchema.cs
Assets\GameScript\Editor\YooScanner\Schema\AssetPrefabSchema.cs
Assets\GameScript\Editor\YooScanner\Schema\AssetTextureSchema.cs
Assets\GameScript\Editor\YooScanner\Schema\DependencySchema.cs
Assets\PlayMaker\Actions\Animator\Editor\BaseClasses\OnAnimatorUpdateActionEditorBase.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorAnimatorRootActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorBodyActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorBoneGameObjectActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorBoolActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorCurrentStateInfoActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorCurrentStateInfoIsNameActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorCurrentStateInfoIsTagActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorCurrentTransitionInfoActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorCurrentTransitionInfoIsNameActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorCurrentTransitionInfoIsUserNameActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorDeltaActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorFloatActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorGravityWeightActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorIKGoalActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorIntActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorIsLayerInTransitionActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorIsMatchingTargetActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorLayerWeightActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorNextStateInfoActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorPivotActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorSpeedActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\GetAnimatorTargetActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\SetAnimatorBoolActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\SetAnimatorFloatActionEditor.cs
Assets\PlayMaker\Actions\Animator\Editor\SetAnimatorIntActionEditor.cs
Assets\PlayMaker\Actions\Debug\Editor\CommentEditor.cs
Assets\PlayMaker\Actions\Editor\CallMethodEditor.cs
Assets\PlayMaker\Actions\Editor\GetDistanceEditor.cs
Assets\PlayMaker\Actions\Editor\LookAtActionEditor.cs
Assets\PlayMaker\Actions\Editor\MoveTowardsActionEditor.cs
Assets\PlayMaker\Actions\Editor\PlayMakerEventProxyEditor.cs
Assets\PlayMaker\Actions\Editor\RunFsmEditor.cs
Assets\PlayMaker\Actions\Editor\SetCameraFOVActionEditor.cs
Assets\PlayMaker\Actions\Editor\SetPropertyEditor.cs
Assets\PlayMaker\Actions\Math\Editor\Vector2RandomValueEditor.cs
Assets\PlayMaker\Actions\Physics\Editor\FindOverlapsEditor.cs
Assets\PlayMaker\Actions\Physics\Editor\RaycastAllEditor.cs
Assets\PlayMaker\Actions\Physics2D\Editor\SetVelocity2dEditor.cs
Assets\PlayMaker\Actions\Physics2D\Editor\SmoothLookAt2dEditor.cs
Assets\PlayMaker\Actions\Quaternion\Editor\GetQuaternionEulerAnglesCustomEditor.cs
Assets\PlayMaker\Actions\Quaternion\Editor\GetQuaternionFromRotationCustomEditor.cs
Assets\PlayMaker\Actions\Quaternion\Editor\GetQuaternionMultipliedByQuaternionCustomEditor.cs
Assets\PlayMaker\Actions\Quaternion\Editor\GetQuaternionMultipliedByVectorCustomEditor.cs
Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionAngleAxisCustomEditor.cs
Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionCustomEditorBase.cs
Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionEulerCustomEditor.cs
Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionInverseCustomEditor.cs
Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionLerpCustomEditor.cs
Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionLookRotationCustomEditor.cs
Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionLowPassFilterCustomEditor.cs
Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionRotateTowardsCustomEditor.cs
Assets\PlayMaker\Actions\Quaternion\Editor\QuaternionSlerpCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\GetSceneBuildIndexCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\GetSceneIsDirtyCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\GetSceneIsLoadedCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\GetSceneIsValidCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\GetSceneNameCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\GetScenePathCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\GetSceneRootCountCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\GetSceneRootGameObjectsCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\Internal\GetSceneActionBaseCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\LoadSceneAsynchCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\LoadSceneCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\MergeScenesCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\MoveGameObjectToSceneCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\SetActiveSceneCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\UnLoadSceneAsynchCustomEditor.cs
Assets\PlayMaker\Actions\SceneManager\Editor\UnLoadSceneCustomEditor.cs
Assets\PlayMaker\Actions\StateMachine\Editor\BlockEventsEditor.cs
Assets\PlayMaker\Actions\Tween\Editor\TweenCameraEditor.cs
Assets\PlayMaker\Actions\Tween\Editor\TweenColorEditor.cs
Assets\PlayMaker\Actions\Tween\Editor\TweenEditorBase.cs
Assets\PlayMaker\Actions\Tween\Editor\TweenFadeEditor.cs
Assets\PlayMaker\Actions\Tween\Editor\TweenPositionEditor.cs
Assets\PlayMaker\Actions\Tween\Editor\TweenPropertyEditor.cs
Assets\PlayMaker\Actions\Tween\Editor\TweenPunchEditor.cs
Assets\PlayMaker\Actions\Tween\Editor\TweenQuaternionEditor.cs
Assets\PlayMaker\Actions\Tween\Editor\TweenRotationEditor.cs
Assets\PlayMaker\Actions\Tween\Editor\TweenScaleEditor.cs
Assets\PlayMaker\Actions\Tween\Editor\TweenUiPositionEditor.cs
Assets\PlayMaker\Actions\Tween\Editor\TweenUiSizeEditor.cs
Assets\PlayMaker\Actions\Tween\Editor\TweenVariableEditor.cs
Assets\PlayMaker\Actions\UI\Editor\UiIcons.cs
Assets\PlayMaker\Editor\AboutWindow.cs
Assets\PlayMaker\Editor\AssetGUIDs.cs
Assets\PlayMaker\Editor\BaseGuidedTourWindow.cs
Assets\PlayMaker\Editor\ContextToolWindow.cs
Assets\PlayMaker\Editor\DefinesHelper.cs
Assets\PlayMaker\Editor\EditorStartupPrefs.cs
Assets\PlayMaker\Editor\FsmActionWindow.cs
Assets\PlayMaker\Editor\FsmComponentInspector.cs
Assets\PlayMaker\Editor\FsmEditorWindow.cs
Assets\PlayMaker\Editor\FsmErrorWindow.cs
Assets\PlayMaker\Editor\FsmEventsWindow.cs
Assets\PlayMaker\Editor\FsmGlobalsWindow.cs
Assets\PlayMaker\Editor\FsmLogWindow.cs
Assets\PlayMaker\Editor\FsmSelectorWindow.cs
Assets\PlayMaker\Editor\FsmStateWindow.cs
Assets\PlayMaker\Editor\FsmTemplate.cs
Assets\PlayMaker\Editor\FsmTemplateEditor.cs
Assets\PlayMaker\Editor\FsmTemplateWindow.cs
Assets\PlayMaker\Editor\FsmTimelineWindow.cs
Assets\PlayMaker\Editor\PlayMakerAddonManager.cs
Assets\PlayMaker\Editor\PlayMakerAutoUpdater.cs
Assets\PlayMaker\Editor\PlayMakerBugReportWindow.cs
Assets\PlayMaker\Editor\PlayMakerBuildCallbacks.cs
Assets\PlayMaker\Editor\PlayMakerControlsEditor.cs
Assets\PlayMaker\Editor\PlayMakerCustomActionWizard.cs
Assets\PlayMaker\Editor\PlayMakerDefines.cs
Assets\PlayMaker\Editor\PlayMakerEditorStartup.cs
Assets\PlayMaker\Editor\PlayMakerGUIInspector.cs
Assets\PlayMaker\Editor\PlayMakerGlobalsInspector.cs
Assets\PlayMaker\Editor\PlayMakerGuidedTour.cs
Assets\PlayMaker\Editor\PlayMakerMainMenu.cs
Assets\PlayMaker\Editor\PlayMakerProjectTools.cs
Assets\PlayMaker\Editor\PlayMakerUpdater.cs
Assets\PlayMaker\Editor\PlayMakerUpgradeGuide.cs
Assets\PlayMaker\Editor\PlayMakerUpgradeTools.cs
Assets\PlayMaker\Editor\PlayMakerWelcomeWindow.cs
Assets\PlayMaker\Editor\PreUpdateChecker.cs
Assets\PlayMaker\Editor\ReportWindow.cs
"Assets\PlayMaker\Extension\PlayMaker Utils\Components\Editor\CommentInspector.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerCurrentEventDataEditor.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerEditorUtils.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerInspectorUtils.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerInspectorUtils_Events.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerInspectorUtils_Reflection.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerInspectorUtils_Variables.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerStats.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Editor\PlayMakerUtilsDefine.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\ElementAdderMenuBuilder.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\ElementAdderMenuCommandAttribute.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\ElementAdderMeta.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\GenericElementAdderMenu.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\GenericElementAdderMenuBuilder.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\IElementAdder.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\IElementAdderMenu.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\IElementAdderMenuBuilder.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Element Adder Menu\IElementAdderMenuCommand.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\GenericListAdaptor.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\IReorderableListAdaptor.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\IReorderableListDropTarget.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Internal\GUIHelper.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Internal\ReorderableListResources.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\Internal\SerializedPropertyUtility.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\ReorderableListControl.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\ReorderableListEvents.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\ReorderableListFlags.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\ReorderableListGUI.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\ReorderableListStyles.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\External Libraries\Rotorz\Reorderable List Field\Editor\SerializedPropertyAdaptor.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\ButtonAttribute\Editor\ButtonDrawer.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\Editor\PlayMakerPropertyDrawerBaseClass.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\MainCameraTarget\Editor\MainCameraTargetPropertyDrawer.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\Owner\Editor\OwnerPropertyDrawer.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerEvent\Editor\PlayMakerEventPropertyDrawer.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerEventTarget\Editor\PlayMakerEventTargetPropertyDrawer.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerFsmTarget\Editor\PlayMakerFsmTargetPropertyDrawer.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerFsmVariable\Editor\PlayMakerFsmVariablePropertyDrawer.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerFsmVariableTarget\Editor\PlayMakerFsmVariableTargetPropertyDrawer.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\PropertyDrawers\PlayMakerTimelineEventTarget\Editor\PlayMakerTimelineEventTargetPropertyDrawer.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\EnumCreator\Editor\EnumCreator.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\EnumCreator\Editor\EnumCreatorWizard.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\EnumCreator\Editor\EnumFileFinder.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\LinkerWizard\Editor\LinkerDataCustomInspector.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\LinkerWizard\Editor\LinkerEditorChecks.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\PlayMakerEventProxy\Editor\EventProxyFileFinder.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\PlayMakerEventProxy\Editor\PlayMakerEventProxyCreator.cs"
"Assets\PlayMaker\Extension\PlayMaker Utils\Wizards\PlayMakerEventProxy\Editor\PlayMakerEventProxyCreatorWizard.cs"
"Assets\PlayMaker\Extension\PlayMaker uGui\Editor\PlayMakerUGuiCanvasRaycastFilterEventsProxyInspector.cs"
"Assets\PlayMaker\Extension\PlayMaker uGui\Editor\PlayMakerUGuiComponentProxyInspector.cs"
"Assets\PlayMaker\Extension\PlayMaker uGui\Editor\PlayMakerUGuiDragEventsProxyInspector.cs"
"Assets\PlayMaker\Extension\PlayMaker uGui\Editor\PlayMakerUGuiDropEventsProxyInspector.cs"
"Assets\PlayMaker\Extension\PlayMaker uGui\Editor\PlayMakerUGuiPointerEventsProxyInspector.cs"
Assets\ThirdPart\Unity-Logs-Viewer\Reporter\Editor\ReporterEditor.cs
"Assets\[废弃]\Tools\Editor\MaterialCleaner.cs"
"Assets\[废弃]\Tools\Editor\MaterialPreviewEditor.cs"
"Assets\[废弃]\Tools\Editor\ResetMaterialTool.cs"
"Assets\[废弃]\Tools\RenameTool\Editor\RBPackageExporter.cs"
"Assets\[废弃]\Tools\RenameTool\Editor\RBPackageExporterCLI.cs"
"Assets\[废弃]\Tools\RenameTool\Editor\RBPackageExporterWindow.cs"
