using UnityEditor;
using HutongGames.PlayMakerEditor;
using Fish.PlayMaker;
using UnityEngine;
using System.Linq;

namespace Fish.PlayMakerEditor
{
	[CustomActionEditor(typeof(GActionFsm))]
	public class GActionUniversalFsmEditor : CustomActionEditor
	{
		public override bool OnGUI()
		{
			var action = target as GActionFsm;
			EditField("targetObject");

			EditorGUILayout.Space();

			DrawSubActionProperties(action.Action, 0);

			EditField("finishedEvent");

			return GUI.changed;
		}

		private void DrawSubActionProperties(PMGAction subAction, int depth)
		{
			// 显示动作类型选择
			GActionFsm.ActionType newActionType;
			// 过滤掉不允许嵌套的动作类型
			var currentType = subAction.ActionType;
			if (currentType == GActionFsm.ActionType.Repeat ||
				currentType == GActionFsm.ActionType.RepeatForever ||
				currentType == GActionFsm.ActionType.Spawn ||
				currentType == GActionFsm.ActionType.Sequence)
			{
				currentType = GActionFsm.ActionType.Show; // 重置为基础类型
			}

			var filteredValues = System.Enum.GetValues(typeof(GActionFsm.ActionType))
				.Cast<GActionFsm.ActionType>()
				.Where(x => x != GActionFsm.ActionType.Repeat &&
						   x != GActionFsm.ActionType.Spawn &&
						   x != GActionFsm.ActionType.RepeatForever &&
						   x != GActionFsm.ActionType.Sequence)
				.ToArray();

			int selectedIndex = System.Array.IndexOf(filteredValues, currentType);
			selectedIndex = EditorGUILayout.Popup("Type", selectedIndex, filteredValues.Select(x => x.ToString()).ToArray());
			newActionType = filteredValues[selectedIndex];

			if (newActionType != subAction.ActionType)
			{
				subAction.ActionType = newActionType;
				subAction.OnActionTypeChanged(newActionType);
			}
			
			

			// 根据类型显示对应的属性
			switch (subAction.ActionType)
			{
				case GActionFsm.ActionType.Show:
					break;
				case GActionFsm.ActionType.Hide:
					break;
				case GActionFsm.ActionType.RemoveSelf:
					break;
				case GActionFsm.ActionType.FlipX:
					break;
				case GActionFsm.ActionType.FlipY:
					break;

				case GActionFsm.ActionType.CallFunc:
					// todo:
					break;
				case GActionFsm.ActionType.DelayTime:
					{
						var action = subAction.Param as PMGActionDelayTime;
						DrawParam("Duration", action.duration);
						EditorGUILayout.Space();
					}
					break;
				case GActionFsm.ActionType.MoveBy:
					{
					}
					break;

				case GActionFsm.ActionType.MoveTo:
					{
						var action = subAction.Param as PMGActionMoveTo;
						DrawParam("Duration", action.duration);
						DrawParam("Target", action.target);
						DrawParam("WorldPosition", action.worldPosition);
						EditorGUILayout.Space();
					}

					break;
				case GActionFsm.ActionType.RotateBy:
					{
					}
					break;
				case GActionFsm.ActionType.RotateTo:
					{
					}
					break;
				case GActionFsm.ActionType.RotateBy3D:
					{
					}
					break;
				case GActionFsm.ActionType.ScaleTo:
					{
					}
					break;
				case GActionFsm.ActionType.ScaleBy:
					{
					}
					break;
				case GActionFsm.ActionType.FadeTo:
					{
					}
					break;
				case GActionFsm.ActionType.TintTo:
					{
					}
					break;
				case GActionFsm.ActionType.BlendTo:
					{
					}
					break;
				case GActionFsm.ActionType.Flash:
					break;
				case GActionFsm.ActionType.BezierTo:
					{
					}
					break;
				case GActionFsm.ActionType.CanvasGroupAlphaFadeTo:
					{
					}
					break;
				case GActionFsm.ActionType.Sequence:
					{
					}
					break;
				case GActionFsm.ActionType.Spawn:
					{
						// todo:
					}
					break;
				case GActionFsm.ActionType.Repeat:
					{
						// todo:
					}
					break;
				case GActionFsm.ActionType.RepeatForever:
					{
						// todo:
					}
					break;
			}
		}

		private void DrawParam<T>(string fieldName, PMParam<T> param)
		{
			EditorGUILayout.LabelField(fieldName, EditorStyles.boldLabel);

			param.UseStoreData = EditorGUILayout.Toggle("UseStoreData", param.UseStoreData);

			if (param.UseStoreData)
			{
				param.StoreKey = EditorGUILayout.TextField("StoreKey", param.StoreKey);
			}
			else
			{
				if (typeof(T) == typeof(string))
				{
					param.RawValue = (T)(object)EditorGUILayout.TextField("Value", (string)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(int))
				{
					param.RawValue = (T)(object)EditorGUILayout.IntField("Value", (int)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(long))
				{
					param.RawValue = (T)(object)EditorGUILayout.LongField("Value", (long)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(float))
				{
					param.RawValue = (T)(object)EditorGUILayout.FloatField("Value", (float)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(double))
				{
					param.RawValue = (T)(object)EditorGUILayout.DoubleField("Value", (double)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(bool))
				{
					param.RawValue = (T)(object)EditorGUILayout.Toggle("Value", (bool)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(Vector2))
				{
					param.RawValue = (T)(object)EditorGUILayout.Vector2Field("Value", (Vector2)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(Vector3))
				{
					param.RawValue = (T)(object)EditorGUILayout.Vector3Field("Value", (Vector3)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(Vector4))
				{
					param.RawValue = (T)(object)EditorGUILayout.Vector4Field("Value", (Vector4)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(Color))
				{
					param.RawValue = (T)(object)EditorGUILayout.ColorField("Value", (Color)(object)param.RawValue);
				}
				else
				{
					EditorGUILayout.LabelField("不支持的类型");
				}
			}

			EditorGUILayout.Space();
		}
	}
}